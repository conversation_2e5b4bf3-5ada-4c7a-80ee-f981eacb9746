import { useState, useEffect, useCallback, useRef } from 'react';
import { useServerSentEvents } from './useServerSentEvents';
import { usePolling } from './usePolling';
import { toast } from 'sonner';

export interface RealTimeUpdate {
  id: string;
  type: string;
  data: any;
  timestamp: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  source: 'sse' | 'polling';
}

export interface RealTimeStats {
  isConnected: boolean;
  connectionMethod: 'sse' | 'polling' | 'both' | 'none';
  sseStatus: 'connected' | 'connecting' | 'disconnected' | 'error';
  pollingStatus: 'active' | 'inactive' | 'error';
  totalUpdatesReceived: number;
  lastUpdate: RealTimeUpdate | null;
  connectionQuality: 'excellent' | 'good' | 'poor' | 'offline';
}

export interface UseRealTimeOptions {
  preferSSE?: boolean;
  enablePollingFallback?: boolean;
  pollingTypes?: string[];
  onUpdate?: (update: RealTimeUpdate) => void;
  onConnectionChange?: (stats: RealTimeStats) => void;
  autoConnect?: boolean;
}

export const useRealTime = (options: UseRealTimeOptions = {}) => {
  const {
    preferSSE = true,
    enablePollingFallback = true,
    pollingTypes = ['*'],
    onUpdate,
    onConnectionChange,
    autoConnect = true
  } = options;

  const [stats, setStats] = useState<RealTimeStats>({
    isConnected: false,
    connectionMethod: 'none',
    sseStatus: 'disconnected',
    pollingStatus: 'inactive',
    totalUpdatesReceived: 0,
    lastUpdate: null,
    connectionQuality: 'offline'
  });

  const updateHandlerRef = useRef<Set<string>>(new Set()); // Track processed update IDs
  const connectionQualityTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // SSE Hook
  const sseHook = useServerSentEvents({
    autoConnect: autoConnect && preferSSE,
    onMessage: (message) => {
      handleUpdate({
        ...message,
        source: 'sse'
      });
    },
    onConnect: () => {
      updateSSEStatus('connected');
    },
    onDisconnect: () => {
      updateSSEStatus('disconnected');
    },
    onError: () => {
      updateSSEStatus('error');
    }
  });

  // Polling Hook
  const pollingHook = usePolling({
    types: pollingTypes,
    autoStart: autoConnect && (enablePollingFallback || !preferSSE),
    onUpdate: (update) => {
      handleUpdate({
        ...update,
        source: 'polling'
      });
    },
    onError: () => {
      updatePollingStatus('error');
    }
  });

  /**
   * Handle incoming updates from either source
   */
  const handleUpdate = useCallback((update: RealTimeUpdate) => {
    // Prevent duplicate processing
    if (updateHandlerRef.current.has(update.id)) {
      return;
    }
    updateHandlerRef.current.add(update.id);

    // Clean up old IDs to prevent memory leak
    if (updateHandlerRef.current.size > 1000) {
      const oldIds = Array.from(updateHandlerRef.current).slice(0, 500);
      oldIds.forEach(id => updateHandlerRef.current.delete(id));
    }

    setStats(prev => ({
      ...prev,
      totalUpdatesReceived: prev.totalUpdatesReceived + 1,
      lastUpdate: update
    }));

    onUpdate?.(update);

    // Update connection quality based on update frequency
    updateConnectionQuality();
  }, [onUpdate]);

  /**
   * Update SSE status and recalculate overall connection status
   */
  const updateSSEStatus = useCallback((status: 'connected' | 'connecting' | 'disconnected' | 'error') => {
    setStats(prev => {
      const newStats = {
        ...prev,
        sseStatus: status
      };
      
      return {
        ...newStats,
        ...calculateConnectionStatus(newStats)
      };
    });
  }, []);

  /**
   * Update polling status and recalculate overall connection status
   */
  const updatePollingStatus = useCallback((status: 'active' | 'inactive' | 'error') => {
    setStats(prev => {
      const newStats = {
        ...prev,
        pollingStatus: status
      };
      
      return {
        ...newStats,
        ...calculateConnectionStatus(newStats)
      };
    });
  }, []);

  /**
   * Calculate overall connection status based on SSE and polling states
   */
  const calculateConnectionStatus = (currentStats: RealTimeStats) => {
    const sseConnected = currentStats.sseStatus === 'connected';
    const pollingActive = currentStats.pollingStatus === 'active';

    let connectionMethod: RealTimeStats['connectionMethod'] = 'none';
    let isConnected = false;

    if (sseConnected && pollingActive) {
      connectionMethod = 'both';
      isConnected = true;
    } else if (sseConnected) {
      connectionMethod = 'sse';
      isConnected = true;
    } else if (pollingActive) {
      connectionMethod = 'polling';
      isConnected = true;
    }

    return {
      isConnected,
      connectionMethod
    };
  };

  /**
   * Update connection quality based on recent activity
   */
  const updateConnectionQuality = useCallback(() => {
    if (connectionQualityTimeoutRef.current) {
      clearTimeout(connectionQualityTimeoutRef.current);
    }

    // Set quality based on connection method
    let quality: RealTimeStats['connectionQuality'] = 'offline';
    
    if (stats.sseStatus === 'connected' && stats.pollingStatus === 'active') {
      quality = 'excellent';
    } else if (stats.sseStatus === 'connected') {
      quality = 'good';
    } else if (stats.pollingStatus === 'active') {
      quality = 'good';
    } else if (stats.sseStatus === 'error' || stats.pollingStatus === 'error') {
      quality = 'poor';
    }

    setStats(prev => ({
      ...prev,
      connectionQuality: quality
    }));

    // Degrade quality if no updates received for a while
    connectionQualityTimeoutRef.current = setTimeout(() => {
      setStats(prev => ({
        ...prev,
        connectionQuality: prev.connectionQuality === 'excellent' ? 'good' : 
                          prev.connectionQuality === 'good' ? 'poor' : 'offline'
      }));
    }, 60000); // 1 minute
  }, [stats.sseStatus, stats.pollingStatus]);

  /**
   * Manual connection methods
   */
  const connect = useCallback(() => {
    if (preferSSE) {
      sseHook.connect();
    }
    if (enablePollingFallback || !preferSSE) {
      pollingHook.start();
    }
  }, [preferSSE, enablePollingFallback, sseHook, pollingHook]);

  const disconnect = useCallback(() => {
    sseHook.disconnect();
    pollingHook.stop();
  }, [sseHook, pollingHook]);

  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(() => {
      connect();
    }, 1000);
  }, [connect, disconnect]);

  /**
   * Fallback management
   */
  useEffect(() => {
    if (!enablePollingFallback) return;

    // Start polling if SSE fails
    if (preferSSE && sseHook.connectionError && !pollingHook.isActive) {
      console.log('SSE failed, falling back to polling');
      pollingHook.start();
      
      toast.info('Switched to polling mode', {
        description: 'Real-time updates continue via polling',
        duration: 3000,
      });
    }

    // Stop polling if SSE recovers
    if (preferSSE && sseHook.isConnected && pollingHook.isActive) {
      console.log('SSE recovered, stopping polling fallback');
      pollingHook.stop();
    }
  }, [preferSSE, enablePollingFallback, sseHook.isConnected, sseHook.connectionError, pollingHook.isActive, pollingHook]);

  /**
   * Update polling status based on hook state
   */
  useEffect(() => {
    updatePollingStatus(pollingHook.isActive ? 'active' : 'inactive');
  }, [pollingHook.isActive, updatePollingStatus]);

  /**
   * Update SSE status based on hook state
   */
  useEffect(() => {
    if (sseHook.isConnecting) {
      updateSSEStatus('connecting');
    } else if (sseHook.isConnected) {
      updateSSEStatus('connected');
    } else if (sseHook.connectionError) {
      updateSSEStatus('error');
    } else {
      updateSSEStatus('disconnected');
    }
  }, [sseHook.isConnected, sseHook.isConnecting, sseHook.connectionError, updateSSEStatus]);

  /**
   * Notify about connection changes
   */
  useEffect(() => {
    onConnectionChange?.(stats);
  }, [stats, onConnectionChange]);

  /**
   * Cleanup
   */
  useEffect(() => {
    return () => {
      if (connectionQualityTimeoutRef.current) {
        clearTimeout(connectionQualityTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...stats,
    connect,
    disconnect,
    reconnect,
    // Expose individual hook methods for advanced usage
    sse: {
      isConnected: sseHook.isConnected,
      isConnecting: sseHook.isConnecting,
      connectionError: sseHook.connectionError,
      reconnect: sseHook.reconnect
    },
    polling: {
      isActive: pollingHook.isActive,
      subscriptionId: pollingHook.subscriptionId,
      triggerPoll: pollingHook.triggerPoll,
      currentInterval: pollingHook.currentInterval
    }
  };
};
