import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import { Logger } from '../config/logger';
import AppError from '../errors/AppError';
import httpStatus from 'http-status';
import { JwtUserPayload } from '../interface/auth';
import { sseAuthService } from '../services/auth/SSEAuthService';
import { authAuditService } from '../services/audit/AuthAuditService';
import { sseHealthMonitor } from '../services/sse/SSEHealthMonitor';
import { sseErrorHandler as enterpriseErrorHandler } from '../services/sse/SSEErrorHandler';

export interface AuthenticatedSSERequest extends Request {
  user?: JwtUserPayload;
  tokenRefreshAttempted?: boolean;
  originalToken?: string;
  connectionId?: string;
}

export interface SSEAuthError {
  type: 'token_expired' | 'token_invalid' | 'token_missing' | 'refresh_failed' | 'auth_failed';
  message: string;
  canRetry: boolean;
  retryAfter?: number;
  refreshRequired?: boolean;
}



/**
 * Enhanced authentication middleware for SSE connections with enterprise-level features
 */
export const authenticateSSE = async (
  req: AuthenticatedSSERequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const startTime = Date.now();

  try {
    // Get token from query parameter or Authorization header
    let token = req.query.token as string;
    let refreshToken = req.query.refreshToken as string;

    if (!token) {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
      }
    }

    // Also check for refresh token in cookies
    if (!refreshToken && req.headers.cookie) {
      const cookies = req.headers.cookie.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        acc[key] = value;
        return acc;
      }, {} as Record<string, string>);
      refreshToken = cookies.refreshToken;
    }

    if (!token) {
      throw new AppError(httpStatus.UNAUTHORIZED, 'Access token is required for SSE connection');
    }

    // Store original token for potential refresh
    req.originalToken = token;

    // Generate client ID for tracking
    const clientId = `${req.ip}_${req.headers['user-agent']?.slice(0, 50) || 'unknown'}`;

    // Use enterprise authentication service with retry logic
    const authResult = await sseAuthService.authenticateSSEConnection(
      token,
      refreshToken,
      clientId,
      {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 30000,
        backoffMultiplier: 2
      }
    );

    if (!authResult.success || !authResult.user) {
      const error = authResult.error!;
      const appError = new AppError(httpStatus.UNAUTHORIZED, error.message);
      (appError as any).authError = error;
      throw appError;
    }

    // Set user information
    req.user = authResult.user;

    // If new tokens were generated, include them in response headers
    if (authResult.newTokens) {
      res.setHeader('X-New-Access-Token', authResult.newTokens.accessToken);
      res.setHeader('X-New-Refresh-Token', authResult.newTokens.refreshToken);
      res.setHeader('X-Token-Refreshed', 'true');
      req.tokenRefreshAttempted = true;

      // Log token refresh event
      await authAuditService.logTokenRefresh(
        authResult.user._id,
        authResult.user.email,
        'sse',
        {
          ip: req.ip,
          userAgent: req.headers['user-agent'],
          endpoint: req.originalUrl
        }
      );

      Logger.info(`Token refreshed for SSE user: ${authResult.user._id} (${authResult.user.role})`);
    }

    // Log successful authentication
    await authAuditService.logAuthSuccess(
      authResult.user._id,
      authResult.user.email,
      'sse',
      {
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        endpoint: req.originalUrl
      }
    );

    const duration = Date.now() - startTime;
    Logger.info(`SSE authentication successful for user: ${authResult.user._id} (${authResult.user.role}) - Duration: ${duration}ms`);

    next();
  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error(`SSE authentication failed - Duration: ${duration}ms:`, error);

    // Log authentication failure
    await authAuditService.logAuthFailure(
      req.user?.email,
      'sse',
      error instanceof Error ? error.name : 'UnknownError',
      error instanceof Error ? error.message : 'Authentication failed',
      {
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        endpoint: req.originalUrl
      }
    );

    await enterpriseErrorHandler.handleError(error, req, res, {
      operation: 'authentication',
      duration: Date.now() - startTime
    });
  }
};



/**
 * Rate limiting middleware for SSE connections
 */
export const sseRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 SSE connections per windowMs
  message: {
    error: 'Too many SSE connection attempts',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    Logger.warn(`SSE rate limit exceeded for IP: ${req.ip}`);
    
    res.writeHead(429, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Retry-After': '900' // 15 minutes in seconds
    });
    
    const errorMessage = {
      id: `rate_limit_${Date.now()}`,
      type: 'rate_limit_error',
      data: {
        error: 'Rate limit exceeded',
        message: 'Too many connection attempts. Please try again later.',
        retryAfter: 900
      },
      timestamp: new Date()
    };
    
    res.write(`event: error\ndata: ${JSON.stringify(errorMessage)}\n\n`);
    res.end();
  }
});

/**
 * CORS middleware for SSE connections
 */
export const sseCORS = (req: Request, res: Response, next: NextFunction): void => {
  const allowedOrigins = process.env.NODE_ENV === 'production'
    ? [
        'https://green-uni-mind-di79.vercel.app',
        'https://green-uni-mind.pages.dev',
        'https://green-uni-mind-backend-oxpo.onrender.com'
      ]
    : [
        'http://localhost:3000',
        'http://localhost:5173',
        'http://localhost:8080',
        'http://localhost:8081',
        'http://localhost:5000'
      ];

  const origin = req.headers.origin;
  
  if (!origin || allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin || '*');
  }
  
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Authorization, Cache-Control');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }
  
  next();
};

/**
 * Connection validation middleware
 */
export const validateSSEConnection = (
  req: AuthenticatedSSERequest,
  res: Response,
  next: NextFunction
): void => {
  // Check if client supports SSE
  const acceptHeader = req.headers.accept;
  if (!acceptHeader || !acceptHeader.includes('text/event-stream')) {
    Logger.warn(`Client does not support SSE: ${req.ip}`);
    
    res.status(400).json({
      error: 'SSE not supported',
      message: 'Client must support Server-Sent Events (text/event-stream)'
    });
    return;
  }

  // Validate user type
  if (!req.user || !['user', 'teacher', 'student'].includes(req.user.role)) {
    Logger.warn(`Invalid user role for SSE connection: ${req.user?.role}`);
    
    res.status(400).json({
      error: 'Invalid user role',
      message: 'User role must be user, teacher, or student'
    });
    return;
  }

  next();
};

/**
 * Connection logging middleware
 */
export const logSSEConnection = async (
  req: AuthenticatedSSERequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const startTime = Date.now();
  const userAgent = req.headers['user-agent'] || 'Unknown';
  const ip = req.ip || req.socket.remoteAddress || 'Unknown';

  Logger.info(`SSE connection attempt from ${ip} - User: ${req.user?._id} (${req.user?.role}) - UA: ${userAgent}`);

  // Register connection with health monitor if user is authenticated
  if (req.user) {
    const connectionId = `${req.user._id}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    req.connectionId = connectionId;

    await sseHealthMonitor.registerConnection(
      connectionId,
      req.user._id,
      req.user.role,
      {
        ip,
        userAgent,
        endpoint: req.originalUrl
      }
    );
  }

  // Log connection duration when it ends
  res.on('close', async () => {
    const duration = Date.now() - startTime;
    Logger.info(`SSE connection closed - Duration: ${duration}ms - User: ${req.user?._id}`);

    if (req.connectionId) {
      await sseHealthMonitor.unregisterConnection(req.connectionId);
    }
  });

  res.on('error', async (error) => {
    const duration = Date.now() - startTime;
    Logger.error(`SSE connection error - Duration: ${duration}ms - User: ${req.user?._id} - Error:`, error);

    if (req.connectionId) {
      await sseHealthMonitor.recordError(req.connectionId, error.message);
    }
  });

  next();
};

/**
 * Health check middleware for SSE endpoint
 */
export const sseHealthCheck = (req: Request, res: Response, next: NextFunction): void => {
  // Simple health check for SSE endpoint
  if (req.path === '/health' || req.path === '/ping') {
    res.json({
      status: 'ok',
      service: 'SSE',
      timestamp: new Date(),
      uptime: process.uptime()
    });
    return;
  }
  
  next();
};

/**
 * Error handling middleware for SSE
 */
export const sseErrorHandler = (
  error: any,
  _req: AuthenticatedSSERequest,
  res: Response,
  _next: NextFunction
): void => {
  Logger.error('SSE middleware error:', error);
  
  if (res.headersSent) {
    // If headers are already sent, we can't send a proper HTTP error
    // Just close the connection
    res.end();
    return;
  }
  
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal server error';
  
  res.writeHead(statusCode, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive'
  });
  
  const errorMessage = {
    id: `error_${Date.now()}`,
    type: 'server_error',
    data: {
      error: 'Server error',
      message: message,
      statusCode: statusCode
    },
    timestamp: new Date()
  };
  
  res.write(`event: error\ndata: ${JSON.stringify(errorMessage)}\n\n`);
  res.end();
};

/**
 * Combined SSE middleware stack
 */
export const sseMiddlewareStack = [
  sseCORS,
  sseHealthCheck,
  sseRateLimit,
  authenticateSSE,
  validateSSEConnection,
  logSSEConnection
];

export default {
  authenticateSSE,
  sseRateLimit,
  sseCORS,
  validateSSEConnection,
  logSSEConnection,
  sseHealthCheck,
  sseErrorHandler,
  sseMiddlewareStack
};
